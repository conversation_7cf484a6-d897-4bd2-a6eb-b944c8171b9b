from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import ContextTypes, ConversationHandler
from datetime import datetime, timezone
from telegram.error import TelegramError
import os
import io
import traceback

# Import conversation states
from src.handlers.command_handlers import WAITING_FOR_CODE

# Import new utility classes
from src.utils.image_manager import ImageManager
from src.utils.message_helpers import MessageHelpers
from src.utils.bot_helpers import BotHelpers
from src.utils.error_handler import ErrorHandler

class CallbackHandlers:
    """Handlers for callback queries"""

    def __init__(self, config, subscription_manager, command_handlers):
        self.config = config
        self.logger = config.get_logger()
        self.sub_manager = subscription_manager
        self.command_handlers = command_handlers

        # Initialize utility classes
        self.image_manager = ImageManager(self.logger)
        self.message_helpers = MessageHelpers(self.logger)
        self.bot_helpers = BotHelpers(config, self.logger)
        self.error_handler = <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(self.logger)

        # We no longer store a static channel_link
        # Instead, we'll generate one-time use links when needed
        self.logger.info("CallbackHandlers initialized with utility classes - will generate one-time use channel links when needed")

    def get_broker_referral_link(self):
        """Get the broker referral link from MongoDB telegram_bots collection"""
        return self.bot_helpers.get_broker_referral_link()

    # Image path methods now use the centralized ImageManager
    def get_welcome_image_path(self):
        """Get the path to the welcome image if it exists"""
        return self.image_manager.get_image_path('welcome')

    def get_membership_offer_image_path(self):
        """Get the path to the membership offer image if it exists"""
        return self.image_manager.get_image_path('membership_offer')

    def get_claim_membership_image_path(self):
        """Get the path to the claim membership image if it exists"""
        return self.image_manager.get_image_path('claim_membership')

    def get_verify_membership_image_path(self):
        """Get the path to the verify membership image if it exists"""
        return self.image_manager.get_image_path('verify_membership')

    def get_withdrawal_image_path(self):
        """Get the path to the withdrawal image if it exists"""
        return self.image_manager.get_image_path('withdrawal')

    def get_support_image_path(self):
        """Get the path to the support image if it exists"""
        return self.image_manager.get_image_path('support')

    def get_bot_id_safely(self, query):
        """Get the bot ID safely from a callback query"""
        return self.message_helpers.get_bot_id_safely(query)

    async def edit_message_safely(self, message, text, reply_markup=None):
        """Edit a message safely, handling both text messages and messages with photos"""
        return await self.message_helpers.edit_message_safely(message, text, reply_markup)

    async def send_message_with_welcome_image(self, message, text, reply_markup=None):
        """Send a new message with the welcome image"""
        return await self.image_manager.send_message_with_image(message, text, 'welcome', reply_markup)

    async def send_message_with_membership_offer_image(self, message, text, reply_markup=None, context=None):
        """Send a new message with the membership offer image"""
        return await self.image_manager.send_message_with_image(message, text, 'membership_offer', reply_markup, context)

    async def send_message_with_claim_membership_image(self, message, text, reply_markup=None, context=None):
        """Send a new message with the claim membership image"""
        return await self.image_manager.send_message_with_image(message, text, 'claim_membership', reply_markup, context)

    async def send_message_with_verify_membership_image(self, message, text, reply_markup=None, context=None):
        """Send a new message with the verify membership image"""
        return await self.image_manager.send_message_with_image(message, text, 'verify_membership', reply_markup, context)

    async def send_message_with_withdrawal_image(self, message, text, reply_markup=None, context=None):
        """Send a new message with the withdrawal image"""
        return await self.image_manager.send_message_with_image(message, text, 'withdrawal', reply_markup, context)

    async def send_message_with_support_image(self, message, text, reply_markup=None, context=None):
        """Send a new message with the support image"""
        return await self.image_manager.send_message_with_image(message, text, 'support', reply_markup, context)

    async def handle_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle all callback queries with anti-spam protection"""
        query = update.callback_query

        callback_data = query.data
        user_id = query.from_user.id

        # Anti-spam protection: Check if user is clicking too fast
        if self.message_helpers.is_user_spamming(user_id, min_interval=1.0):
            await query.answer("⏳ Please wait a moment before clicking again.", show_alert=True)
            return WAITING_FOR_CODE

        # Check if user's request is already being processed
        if self.message_helpers.is_user_being_processed(user_id):
            await query.answer("⏳ Your request is being processed, please wait...", show_alert=True)
            return WAITING_FOR_CODE

        # Answer the callback query to stop the loading animation
        await query.answer()

        # Process the callback with user processing lock
        async def process_callback():
            return await self._handle_callback_internal(update, context, query, callback_data, user_id)

        result = await self.message_helpers.with_user_processing_lock(user_id, process_callback)
        return result if result is not None else WAITING_FOR_CODE

    async def _handle_callback_internal(self, update: Update, context: ContextTypes.DEFAULT_TYPE, query, callback_data: str, user_id: int):
        """Internal callback handling logic"""

        # Save user information to telegram_bots collection
        try:
            # Get the bot's ID from context or config
            bot_id = context.bot.id  # This gets the current bot's Telegram ID

            # Prepare data to update for this specific user interaction
            user_interaction_data = {
                "channel_link_sent": True,
                "interaction_time": datetime.now(timezone.utc),
            }

            # Call method to update the specific bot's data
            success, message = self.sub_manager.update_bot_user_interaction(
                bot_id,
                user_id,
                user_interaction_data
            )

            if success:
                self.logger.info(f"User {user_id} interaction saved for bot {bot_id}")
            else:
                self.logger.error(f"Error saving user {user_id} interaction for bot {bot_id}: {message}")
        except Exception as e:
            self.logger.error(f"Error in bot data update: {str(e)}")

        self.logger.info(f"Callback from user {user_id}: {callback_data}")

        if callback_data == 'new_registration':
            # Show the New Registration submenu
            message_text = (
                "Unlock seamless benefits of joining the 0-cost membership, Get access to :\n\n"
                "Daily Setups and Highly Accurate Signals 🎯\n"
                "Chart Updates 📊\n"
                "Exclusive Forex Trading Course 📹\n"
                "News info & Data releases 📰\n"
                "Priority to your Account-related issues 🥇\n"
                "Premium support on Withdrawals/Deposits & Trades 🤝"
            )

            # Create the keyboard with the registration options
            keyboard = [
                [InlineKeyboardButton("I Want to Join 🏌️", callback_data='reg_want_to_join')],
                [InlineKeyboardButton("Account created ✅", callback_data='account_created')],
                [InlineKeyboardButton("I Already have an Account 🧾", callback_data='partner_change_start')],
                [InlineKeyboardButton("Need more Help 🪃", callback_data='submit_request_start')],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Always send a new message with the membership offer image
            await self.send_message_with_membership_offer_image(query.message, message_text, reply_markup, context)

            # Clear any previous data
            context.user_data.clear()
            return WAITING_FOR_CODE

        elif callback_data == 'enter_code':
            # Check the user's subscription status
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            if status == 'active':
                # User already has active access
                user_data = self.sub_manager.get_user_data(user_id)

                # Prepare the message text
                message_text = (f"You already have active access with code: {user_data['access_code']}\n\n"
                               f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S')}")

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'pending':
                # User has a pending subscription
                self.logger.info(f"User {user_id} has pending subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'denied':
                # User's verification was denied
                self.logger.info(f"User {user_id} has verification denied status, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Reverify 🔄", callback_data="reg_start_verification")],
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'inactive':
                # User has an inactive subscription
                self.logger.info(f"User {user_id} has inactive subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            else:
                # New user - proceed with registration
                self.logger.info(f"User {user_id} has status: {status}, proceeding with registration")
                # Send a new message asking for the access code
                await query.message.reply_text(
                    "Please enter your access code:",
                    reply_markup=ForceReply(selective=True)
                )
                # Clear any previous data
                context.user_data.clear()
                return WAITING_FOR_CODE

        elif callback_data == 'get_code':
            message_text = "To get an access code, please contact our support team or visit our website."
            reply_markup = InlineKeyboardMarkup([[
                InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")
            ]])

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                # Only if editing fails completely, send a new message
                self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                await query.message.reply_text(
                    message_text,
                    reply_markup=reply_markup
                )

        elif callback_data == 'back_to_menu':
            # Always edit the current message when going back to menu
            self.logger.info(f"Going back to menu for user {user_id}, editing current message")
            # Get the subscription menu content
            reply_markup, text = await self.command_handlers.get_subscription_menu()

            # Edit the message safely
            success = await self.edit_message_safely(query.message, text, reply_markup)

            if not success:
                self.logger.warning(f"Failed to edit message for back_to_menu, will not send a new message")

            return WAITING_FOR_CODE

        elif callback_data == 'back_to_verification':
            # Return to the verification success message
            self.logger.info(f"Going back to verification success message for user {user_id}")
            user_data = self.sub_manager.get_user_data(user_id)

            if user_data:
                message_text = f"Congratulations! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                # Create a one-time use invite link
                channel_link = await self.create_one_time_invite_link(context)

                keyboard = [
                    [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                    [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                    [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                    [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+971585727623")],
                    [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                    [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    self.logger.warning(f"Failed to edit message for back_to_verification, will not send a new message")
            else:
                # If user data not found, go back to main menu
                self.logger.warning(f"User data not found for user {user_id}, going back to main menu")
                return await self.handle_callback(update, context, callback_data='back_to_menu')

            return WAITING_FOR_CODE

        elif callback_data == 'sub_status':
            user_data = self.sub_manager.get_user_data(user_id)
            if user_data:
                # Get subscription status
                subscription_status = user_data.get('subscription', 'unknown')
                status_display = subscription_status.capitalize()

                # Get verification status
                user_verify = user_data.get('user_verify', False)
                verify_display = "Verified ✅" if user_verify else "Not Verified ❌"

                # Get broker registration status
                broker_reg = user_data.get('broker_reg', False)
                broker_display = "Registered ✅" if broker_reg else "Not Registered ❌"

                # Add a note for pending status
                status_note = ""
                if subscription_status == 'pending':
                    status_note = "\n\n⏳ Your account is currently under verification. Our team will notify you once the verification is complete."
                elif subscription_status == 'inactive' and not user_verify:
                    status_note = "\n\n⏳ Your account is not yet verified. Our team will verify your account soon, after which it will be activated."

                # Get user status
                user_status = user_data.get('user_status', 'unknown')
                user_status_display = user_status.replace('_', ' ').capitalize()

                message_text = (f"Your subscription details:\n\n"
                              f"• Status: {status_display}\n"
                              f"• User Status: {user_status_display}\n"
                              f"• Verification: {verify_display}\n"
                              f"• Broker Registration: {broker_display}\n"
                              f"• Access code: {user_data.get('access_code', 'Not available')}\n"
                              f"• Telegram Bot ID: {user_data.get('telegram_bot_id', 'Not available')}\n"
                              f"• Subscription date: {user_data.get('registration_time', datetime.now(timezone.utc)).strftime('%Y-%m-%d %H:%M:%S')}{status_note}")
            else:
                message_text = "No subscription information found."

            reply_markup = InlineKeyboardMarkup([[
                InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")
            ]])

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                # Only if editing fails completely, send a new message
                self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                await query.message.reply_text(
                    message_text,
                    reply_markup=reply_markup
                )

        # Step 2: User clicks "I Want to Join 🏌️"
        elif callback_data == 'reg_want_to_join':
            # Check the user's subscription status
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            if status == 'active':
                # User already has active access
                user_data = self.sub_manager.get_user_data(user_id)

                # Prepare the message text
                message_text = (f"You already have active access with code: {user_data['access_code']}\n\n"
                               f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S')}")

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'denied':
                # User's verification was denied
                self.logger.info(f"User {user_id} has verification denied status, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Reverify 🔄", callback_data="reg_start_verification")],
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'pending':
                # User has a pending subscription
                self.logger.info(f"User {user_id} has pending subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'inactive':
                # User has an inactive subscription
                self.logger.info(f"User {user_id} has inactive subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            else:
                # User doesn't have an active subscription, show join steps
                self.logger.info(f"User {user_id} has status: {status}, proceeding with join steps")
                # Get broker referral link from config
                broker_referral_link = self.get_broker_referral_link()

                message_text = (
                    "To join our exclusive membership for FREE, follow these simple steps :\n\n"
                    f"Step 1️⃣Open your account using the link:  {broker_referral_link}\n\n"
                    "⚠️Make sure you are using the partner code: verifyme\n\n"
                    "Step 2️⃣Complete your KYC and verify your full profile\n\n"
                    "Step 3️⃣Create a new trading account under your profile and make a deposit to start trading.\n\n"
                    "Step 4️⃣Share your complete info for the verification process including the trading account number you have created in your profile.\n\n"
                    "Step 5️⃣Await for your profile verification with us and Gain access to our exclusive spaces 🏆\n\n\n"
                    "Kindly Note : Your access code is your Trading Account number (10 digits), if you open a new trading account under the same profile, you would need to reverify your details once to keep the access."
                )

                keyboard = [
                    [InlineKeyboardButton("Opened the Account ✅", callback_data='reg_opened_account')],
                    [InlineKeyboardButton("Have an account but not under this link 🔁", callback_data='partner_change_start')],
                    [InlineKeyboardButton("Back to Registration", callback_data="new_registration")],
                    [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Always send a new message with the membership offer image
                await self.send_message_with_membership_offer_image(query.message, message_text, reply_markup, context)

        # Step 3: User clicks "Opened the Account ✅"
        elif callback_data == 'reg_opened_account':
            # Check the user's subscription status
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            if status == 'active':
                # User already has active access
                user_data = self.sub_manager.get_user_data(user_id)

                # Prepare the message text
                message_text = (f"You already have active access with code: {user_data['access_code']}\n\n"
                               f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S')}")

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'pending':
                # User has a pending subscription
                self.logger.info(f"User {user_id} has pending subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            elif status == 'inactive':
                # User has an inactive subscription
                self.logger.info(f"User {user_id} has inactive subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
            else:
                # User doesn't have an active subscription, proceed with verification
                self.logger.info(f"User {user_id} has status: {status}, proceeding with verification")
                message_text = (
                    "Well done! You have taken the first step to unlock endless possibilities in your trading career 📈\n\n"
                    "Help us with more info about yourself ℹ️"
                )

                keyboard = [
                    [InlineKeyboardButton("Start Verification ✅", callback_data='reg_start_verification')],
                    [InlineKeyboardButton("Back to Previous Step", callback_data="reg_want_to_join")],
                    [InlineKeyboardButton("Back to Registration", callback_data="new_registration")],
                    [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # If editing fails, send a new message with the welcome image
                    await self.send_message_with_welcome_image(query.message, message_text, reply_markup)

        # Handler for "Account Created ✅" button in Claim Membership menu
        elif callback_data == 'account_created':
            # Check the user's subscription status
            user_id = query.from_user.id
            self.logger.info(f"account_created callback for user {user_id}")
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            if status == 'active':
                # User already has active access
                user_data = self.sub_manager.get_user_data(user_id)

                # Prepare the message text
                message_text = (f"You already have active access with code: {user_data['access_code']}\n\n"
                               f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S')}")

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            elif status == 'pending':
                # User has a pending subscription
                self.logger.info(f"User {user_id} has pending subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            elif status == 'inactive':
                # User has an inactive subscription
                self.logger.info(f"User {user_id} has inactive subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            else:
                # User doesn't have an active subscription, proceed with verification
                self.logger.info(f"User {user_id} has status: {status}, proceeding with verification")
                # Step 5: Show the account number image and ask for the account number
                # Get the account number image path
                account_number_image_path = os.path.join('assets', 'account_number_image.png')

                # Check if the image exists
                if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                    self.logger.info(f"Account number image found at {account_number_image_path}")
                    try:
                        with open(account_number_image_path, 'rb') as photo:
                            await query.message.reply_photo(
                                photo=photo,
                                caption="Enter the account number that is available in your trading profile:",
                                reply_markup=ForceReply(selective=True)
                            )
                    except Exception as e:
                        self.logger.error(f"Error sending account number image: {str(e)}")
                        # Fall back to text message if image fails
                        await query.message.reply_text(
                            "Enter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                else:
                    self.logger.warning(f"Account number image not found at {account_number_image_path}")
                    # Send text message if image doesn't exist
                    await query.message.reply_text(
                        "Enter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )

                # Clear any previous data
                context.user_data.clear()

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_ACCOUNT_NUMBER
                return WAITING_FOR_ACCOUNT_NUMBER

        # Handler for "Start Verification ✅" button
        elif callback_data == 'reg_start_verification':
            # Check the user's subscription status
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            # Store the bot ID in context for later use
            telegram_bot_id = self.get_bot_id_safely(update)
            if telegram_bot_id:
                context.user_data['telegram_bot_id'] = telegram_bot_id
                self.logger.info(f"Stored telegram_bot_id in context for user {user_id}: {telegram_bot_id}")

            if status == 'active':
                # User already has active access
                user_data = self.sub_manager.get_user_data(user_id)

                # Prepare the message text
                message_text = (f"You already have active access with code: {user_data['access_code']}\n\n"
                               f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S')}")

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            elif status == 'denied':
                # User's verification was denied, but we'll allow them to reverify
                self.logger.info(f"User {user_id} has verification denied status, but we're allowing reverification")
                # Proceed with verification process
                self.logger.info(f"User {user_id} is reverifying after denial, proceeding with verification")
                # Step 5: Show the account number image and ask for the account number
                # Get the account number image path
                account_number_image_path = os.path.join('assets', 'account_number_image.png')

                # Check if the image exists
                if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                    self.logger.info(f"Account number image found at {account_number_image_path}")
                    try:
                        with open(account_number_image_path, 'rb') as photo:
                            await query.message.reply_photo(
                                photo=photo,
                                caption="Enter the account number that is available in your trading profile:",
                                reply_markup=ForceReply(selective=True)
                            )
                    except Exception as e:
                        self.logger.error(f"Error sending account number image: {str(e)}")
                        # Fall back to text message if image fails
                        await query.message.reply_text(
                            "Enter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                else:
                    self.logger.warning(f"Account number image not found at {account_number_image_path}")
                    # Send text message if image doesn't exist
                    await query.message.reply_text(
                        "Enter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )

                # Clear any previous data
                context.user_data.clear()

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_ACCOUNT_NUMBER
                self.logger.info(f"Setting conversation state to WAITING_FOR_ACCOUNT_NUMBER for user {user_id}")
                return WAITING_FOR_ACCOUNT_NUMBER
            elif status == 'pending':
                # User has a pending subscription
                self.logger.info(f"User {user_id} has pending subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            elif status == 'inactive':
                # User has an inactive subscription
                self.logger.info(f"User {user_id} has inactive subscription, status_message: {status_message}")
                # Prepare the message text
                message_text = status_message

                # Prepare the reply markup
                reply_markup = InlineKeyboardMarkup([
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ])

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    # Only if editing fails completely, send a new message
                    self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                    await query.message.reply_text(
                        message_text,
                        reply_markup=reply_markup
                    )
                return WAITING_FOR_CODE
            else:
                # User doesn't have an active subscription, proceed with verification
                self.logger.info(f"User {user_id} doesn't have an active or pending subscription, proceeding with verification")
                # Step 5: Show the account number image and ask for the account number
                # Get the account number image path
                account_number_image_path = os.path.join('assets', 'account_number_image.png')

                # Check if the image exists
                if os.path.exists(account_number_image_path) and os.path.isfile(account_number_image_path):
                    self.logger.info(f"Account number image found at {account_number_image_path}")
                    try:
                        with open(account_number_image_path, 'rb') as photo:
                            await query.message.reply_photo(
                                photo=photo,
                                caption="Enter the account number that is available in your trading profile:",
                                reply_markup=ForceReply(selective=True)
                            )
                    except Exception as e:
                        self.logger.error(f"Error sending account number image: {str(e)}")
                        # Fall back to text message if image fails
                        await query.message.reply_text(
                            "Enter the account number that is available in your trading profile:",
                            reply_markup=ForceReply(selective=True)
                        )
                else:
                    self.logger.warning(f"Account number image not found at {account_number_image_path}")
                    # Send text message if image doesn't exist
                    await query.message.reply_text(
                        "Enter the account number that is available in your trading profile:",
                        reply_markup=ForceReply(selective=True)
                    )

                # Clear any previous data
                context.user_data.clear()

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_ACCOUNT_NUMBER
                self.logger.info(f"Setting conversation state to WAITING_FOR_ACCOUNT_NUMBER for user {user_id}")
                return WAITING_FOR_ACCOUNT_NUMBER

        # Placeholder handlers for other registration options
        # 'reg_already_have' handler removed - now using 'partner_change_start'
        # 'reg_need_help' handler removed - now using 'submit_request_start'

        # Partner Change Area workflow
        elif callback_data == 'partner_change_start':
            message_text = "If you already have an account on Exness and it's not linked under our IB/Partner Link and you're not able to verify yourself, you can follow these simple steps to change your partner and apply again 🎋 :"

            # First, send the message
            await query.message.reply_text(message_text)

            # Then, send the PDF document from MongoDB
            try:
                # Access the database through config
                if self.config.db:
                    # Query the telegram_bots collection to get bot data with PDF
                    bot_data = self.config.db.db.telegram_bots.find_one(
                        {'is_active': True},
                        {'data.pdf': 1}  # Only fetch the PDF field
                    )

                    if bot_data and 'data' in bot_data and 'pdf' in bot_data['data']:
                        pdf_binary = bot_data['data']['pdf']
                        self.logger.info("Retrieved PDF from MongoDB for partner change guide")

                        # Send the PDF document from binary data
                        pdf_stream = io.BytesIO(pdf_binary)
                        pdf_stream.seek(0)
                        await query.message.reply_document(
                            document=pdf_stream,
                            filename="Simple Steps to Change Partner.pdf"
                        )
                    else:
                        self.logger.warning("PDF not found in MongoDB bot data")
                else:
                    self.logger.error("Database connection not available for PDF retrieval")
            except Exception as e:
                self.logger.error(f"Error retrieving/sending PDF from MongoDB: {str(e)}")

            # Send follow-up message with buttons
            follow_up_message = "Kindly Note: Once you change your partner, it is important to create a new trading account and start applying for it 🥷"
            keyboard = [
                [InlineKeyboardButton("Partner Changed ✅", callback_data='new_registration')],
                [InlineKeyboardButton("Not able to Change Partner ⚠️", callback_data='partner_change_unable')],
                [InlineKeyboardButton("Need additional Support ❓", callback_data='submit_request_start')],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.message.reply_text(follow_up_message, reply_markup=reply_markup)
            return WAITING_FOR_CODE

        elif callback_data == 'partner_changed_confirm':
            message_text = "Great! Please ensure you create a new trading account within your Exness Personal Area to proceed with the application."
            keyboard = [
                [InlineKeyboardButton("Back to Partner Change Area", callback_data="partner_change_start")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        elif callback_data == 'partner_change_unable':
            message_text = "If you're not able to change the partner you can apply by opening a New Account 🪪 :"
            keyboard = [
                [InlineKeyboardButton("Steps to open new account 🆕", callback_data='send_new_account_steps_pdf')],
                [InlineKeyboardButton("Back to previous Menu", callback_data="partner_change_start")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        elif callback_data == 'send_new_account_steps_pdf':
            # Send the PDF document from MongoDB
            try:
                # Access the database through config
                if self.config.db:
                    # Query the telegram_bots collection to get bot data with PDF
                    bot_data = self.config.db.db.telegram_bots.find_one(
                        {'is_active': True},
                        {'data.pdf': 1}  # Only fetch the PDF field
                    )

                    if bot_data and 'data' in bot_data and 'pdf' in bot_data['data']:
                        pdf_binary = bot_data['data']['pdf']
                        self.logger.info("Retrieved PDF from MongoDB for new account steps guide")

                        # Send the PDF document from binary data
                        pdf_stream = io.BytesIO(pdf_binary)
                        await query.message.reply_document(
                            document=pdf_stream,
                            filename="Simple Steps to Open New Account.pdf"
                        )
                    else:
                        self.logger.warning("PDF not found in MongoDB bot data")
                else:
                    self.logger.error("Database connection not available for PDF retrieval")
            except Exception as e:
                self.logger.error(f"Error retrieving/sending PDF from MongoDB: {str(e)}")

            # Send follow-up message with buttons
            follow_up_message = "Follow these steps to open a new account. If you need further assistance, please contact support."
            keyboard = [
                [InlineKeyboardButton("Back to Partner Change Area", callback_data="partner_change_start")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.message.reply_text(follow_up_message, reply_markup=reply_markup)

        # Handlers for account verification workflow buttons
        elif callback_data == 'verification_request_start':
            try:
                user_id = query.from_user.id
                self.logger.info(f"Starting verification_request_start for user {user_id}")

                # Check if user already exists in master_user_data
                user_data = self.sub_manager.get_master_user_data(user_id)

                if user_data and all(key in user_data for key in ['name', 'email', 'whatsapp', 'trading_experience']):
                    # User already exists with complete data - show existing data
                    self.logger.info(f"User {user_id} already exists in master_user_data with complete information")

                    # Store the existing data in context for later use
                    context.user_data['verification_name'] = user_data.get('name')

                    # Send confirmation message
                    message_text = f"Welcome back {user_data.get('name')}! We already have your details.\n\nWe will soon verify your account and notify you once verified.⏳"

                    keyboard = [
                        [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                    ]

                    await query.message.reply_text(
                        message_text,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                    return WAITING_FOR_CODE
                else:
                    # User doesn't exist or has incomplete data - proceed to collect information
                    message_text = "To submit a verification request, we need some information from you. Please provide your details below."

                    # Ask for name first
                    await query.message.reply_text(
                        "👤Your Good Name?",
                        reply_markup=ForceReply(selective=True)
                    )

                    # Clear any previous data
                    context.user_data.clear()
                    self.logger.info(f"Cleared context.user_data for user {query.from_user.id}")

                    # Import the new state here to avoid circular imports
                    from src.handlers.message_handlers import WAITING_FOR_VERIFICATION_REQUEST_NAME
                    self.logger.info(f"Returning state WAITING_FOR_VERIFICATION_REQUEST_NAME for user {query.from_user.id}")
                    return WAITING_FOR_VERIFICATION_REQUEST_NAME
            except Exception as e:
                self.logger.error(f"Error in verification_request_start: {str(e)}")
                traceback.print_exc()
                # Try to send an error message to the user
                try:
                    await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                except Exception:
                    pass
                return WAITING_FOR_CODE

        # Handler for verification request trading experience selection
        elif callback_data in ["beginner", "intermediate", "pro"]:
            try:
                user_id = query.from_user.id
                self.logger.info(f"User {user_id} selected trading experience: {callback_data}")

                # Store the trading experience in context
                context.user_data['verification_trading_experience'] = callback_data

                # Check if we already have an access code in context (from verify_membership flow)
                if 'verification_access_code' in context.user_data:
                    access_code = context.user_data['verification_access_code']
                    self.logger.info(f"Using access code from context for user {user_id}: {access_code}")

                    # Create user details dictionary with trading experience
                    user_details = {
                        'name': context.user_data.get('verification_name', 'Unknown'),
                        'email': context.user_data.get('verification_email', 'Unknown'),
                        'whatsapp': context.user_data.get('verification_whatsapp', 'Unknown'),
                        'trading_experience': callback_data
                    }

                    # Create master user data
                    master_user_data = {
                        'user_id': user_id,
                        'access_code': access_code,
                        'name': user_details['name'],
                        'email': user_details['email'],
                        'whatsapp': user_details['whatsapp'],
                        'trading_experience': callback_data,
                        'subscription': 'unknown',  # Set to unknown for verification requests
                        'user_verify': False,  # Default to not verified
                        'broker_reg': False,    # User doesn't have a valid broker registration yet
                        'user_status': 'verification_pending',  # Pending verification
                        'user_verify_status': 'pending',
                        'registration_time': datetime.now(timezone.utc),
                        'telegram_bot_id': self.get_bot_id_safely(query)  # Add the Telegram bot ID
                    }

                    # Save to master_user_data collection
                    success, auto_verified = self.sub_manager.save_to_master_user_data(master_user_data)

                    if success:
                        self.logger.info(f"Successfully saved verification data for user {user_id} with access code {access_code}")

                        if auto_verified:
                            # If the user was automatically verified, create a one-time use invite link
                            channel_link = await self.create_one_time_invite_link(context)

                            message_text = f"Congratulations! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                            keyboard = [
                                [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                                [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                                [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                                [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+971585727623")],
                                [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                            ]

                            await query.message.reply_text(
                                message_text,
                                reply_markup=InlineKeyboardMarkup(keyboard)
                            )
                        else:
                            # User wasn't auto-verified
                            message_text = "Thank you for submitting your information. We'll review your request."
                            keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]

                            await query.message.reply_text(
                                message_text,
                                reply_markup=InlineKeyboardMarkup(keyboard)
                            )
                    else:
                        self.logger.error(f"Failed to save verification data for user {user_id}")
                        message_text = "There was an issue submitting your verification request. Our team has been notified and will assist you shortly."

                        keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]

                        await query.message.reply_text(
                            message_text,
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )

                    return WAITING_FOR_CODE
                else:
                    # No access code in context, ask for it
                    await query.message.reply_text(
                        "Please enter your Trading Account number (10 digits):",
                        reply_markup=ForceReply(selective=True)
                    )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT
                return WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT
            except Exception as e:
                self.logger.error(f"Error handling trading experience selection: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        # Handler for regular verification trading experience selection
        elif callback_data in ["beginner_verification", "intermediate_verification", "pro_verification"]:
            try:
                user_id = query.from_user.id
                # Extract the actual experience level by removing the '_verification' suffix
                experience = callback_data.replace("_verification", "")
                self.logger.info(f"User {user_id} selected trading experience for verification: {experience}")

                # Store the trading experience in context
                context.user_data['verification_trading_experience'] = experience

                # We'll proceed with the verification process without showing the "Details submitted" message
                # since we're now auto-verifying users with valid access codes

                # Send options for next steps
                keyboard = [
                    [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                ]

                # Get the access code and user ID
                user_id = query.from_user.id
                access_code = context.user_data.get('account_number')

                # Get user data to check if this is an expired user trying to use their old code
                user_data = self.sub_manager.get_user_data(user_id)
                if user_data and (user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired') and user_data.get('access_code') == access_code:
                    # This is an expired user trying to use their old code
                    message_text = "This access code is not valid. Your subscription has expired."
                    keyboard = [[InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification'), InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')], [InlineKeyboardButton("Submit a Manual Verification Request 🔍", callback_data='verification_request_start')], [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')], [InlineKeyboardButton("Back to Menu", callback_data='back_to_menu')]]
                    await query.message.reply_text(
                        message_text,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )
                # Check if the access code is valid and not used by another user
                if access_code:
                    # First check if the code exists in the access_code collection
                    is_valid_in_access_codes = self.sub_manager.db.verify_access_code(access_code)
                    self.logger.info(f"Access code exists in access_code collection: {is_valid_in_access_codes}")

                    # Check if the code is already used by another user
                    existing_users = self.sub_manager.get_users_by_code(access_code)
                    self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                    # Check if there are multiple users with this access code
                    # OR if the current user is not in the list of existing users
                    code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                    self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                    # Check if this is an expired user trying to use their old code
                    user_data = self.sub_manager.get_user_data(user_id)
                    is_expired_user = user_data and (user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired') and user_data.get('access_code') == access_code
                    self.logger.info(f"Is expired user check: {is_expired_user}")

                    # Determine if the access code is valid for this user
                    access_code_valid = is_valid_in_access_codes and not code_used_by_other and not is_expired_user
                    self.logger.info(f"Access code is valid for user {user_id}: {access_code_valid}")

                    if is_expired_user:
                        # This is an expired user trying to use their old code
                        message_text = "This access code is not valid. Your subscription has expired."
                        keyboard = [
                            [InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification')],
                            [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                            [InlineKeyboardButton("Submit a Manual Verification Request 🔍", callback_data='verification_request_start')],
                            [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                            [InlineKeyboardButton("Back to Menu", callback_data='back_to_menu')]
                        ]

                        await query.message.reply_text(
                            message_text,
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
                    elif access_code_valid:
                        # If the access code is valid, we'll auto-verify the user in save_to_master_user_data
                        # so we don't need to show any message here
                        pass
                    else:
                        # Only show a message if the access code is not valid
                        await query.message.reply_text(
                            "Thank you for submitting your information. We'll review your request.",
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )

                # Save the verification data to the database

                # Create user details dictionary with trading experience
                user_details = {
                    'name': context.user_data.get('verification_name', 'Unknown'),
                    'email': context.user_data.get('verification_email', 'Unknown'),
                    'whatsapp': context.user_data.get('verification_whatsapp', 'Unknown'),
                    'trading_experience': experience
                }

                # Create master user data
                master_user_data = {
                    'user_id': user_id,
                    'access_code': access_code,
                    'name': user_details.get('name', 'Unknown'),
                    'email': user_details.get('email', 'Unknown'),
                    'whatsapp': user_details.get('whatsapp', 'Unknown'),
                    'trading_experience': experience,
                    'subscription': 'unknown',  # Set to unknown for verification requests
                    'user_verify': False,  # Default to not verified
                    'broker_reg': False,    # User doesn't have a valid broker registration yet
                    'user_status': 'verification_pending',  # Pending verification
                    'user_verify_status': 'pending',
                    'registration_time': datetime.now(timezone.utc),
                    'telegram_bot_id': self.get_bot_id_safely(query)  # Add the Telegram bot ID
                }

                # Save to master_user_data collection
                success, auto_verified = self.sub_manager.save_to_master_user_data(master_user_data)

                if success:
                    self.logger.info(f"Successfully saved verification data for user {user_id} with access code {access_code}")

                    if auto_verified:
                        # If the user was automatically verified, create a one-time use invite link
                        channel_link = await self.create_one_time_invite_link(context)

                        message_text = f"Congratulations! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                        keyboard = [
                            [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                            [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                            [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                            [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+971585727623")],
                            [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                            [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                        ]

                        await query.message.reply_text(
                            message_text,
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
                    else:
                        # Check if the access code is already used by another user
                        existing_users = self.sub_manager.get_users_by_code(access_code)
                        self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                        # Check if there are multiple users with this access code
                        # OR if the current user is not in the list of existing users
                        code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                        self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                        if code_used_by_other:
                            message_text = "⚠️ This access code is already associated with another user and cannot be used again. Please try a different access code or contact support for assistance.\n\nAccess code: " + access_code
                        else:
                            message_text = "Thank you for submitting your information. We'll review your request."

                        keyboard = [
                            [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                        ]

                        await query.message.reply_text(
                            message_text,
                            reply_markup=InlineKeyboardMarkup(keyboard)
                        )
                else:
                    self.logger.error(f"Failed to save verification data for user {user_id}")
                    message_text = "There was an issue submitting your verification request. Our team has been notified and will assist you shortly."

                    keyboard = [
                        [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                    ]

                    await query.message.reply_text(
                        message_text,
                        reply_markup=InlineKeyboardMarkup(keyboard)
                    )

                return WAITING_FOR_CODE
            except Exception as e:
                self.logger.error(f"Error handling verification trading experience: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        # Handlers for using existing user data
        elif callback_data == 'verification_use_existing':
            try:
                user_id = query.from_user.id
                self.logger.info(f"User {user_id} chose to proceed with existing details")

                # Get the account number from context
                access_code = context.user_data.get('account_number')

                if not access_code:
                    self.logger.error(f"No account number found in context for user {user_id}")
                    await query.message.reply_text("Sorry, there was an error processing your request. Please try again.")
                    return WAITING_FOR_CODE

                # Get user data from context
                user_details = {
                    'name': context.user_data.get('verification_name', 'Unknown'),
                    'email': context.user_data.get('verification_email', 'Unknown'),
                    'whatsapp': context.user_data.get('verification_whatsapp', 'Unknown'),
                    'trading_experience': context.user_data.get('verification_trading_experience', 'Unknown')
                }

                # Create master user data
                master_user_data = {
                    'user_id': user_id,
                    'access_code': access_code,
                    'name': user_details['name'],
                    'email': user_details['email'],
                    'whatsapp': user_details['whatsapp'],
                    'trading_experience': user_details['trading_experience'],
                    'subscription': 'unknown',  # Set to unknown for verification requests
                    'user_verify': False,  # Default to not verified
                    'broker_reg': False,    # User doesn't have a valid broker registration yet
                    'user_status': 'verification_pending',  # Pending verification
                    'user_verify_status': 'pending',
                    'registration_time': datetime.now(timezone.utc),
                    'telegram_bot_id': self.get_bot_id_safely(query)  # Add the Telegram bot ID
                }

                # Save to master_user_data collection
                success, auto_verified = self.sub_manager.save_to_master_user_data(master_user_data)

                if success:
                    self.logger.info(f"Successfully saved verification data for user {user_id} with access code {access_code}")

                    if auto_verified:
                        # If the user was automatically verified, create a one-time use invite link
                        channel_link = await self.create_one_time_invite_link(context)

                        message_text = f"Congratulations! Your account has been verified automatically. ✅\n\nYou can now join our exclusive channel to access all membership benefits:\n\nClick the button below to join now! 🚀"

                        keyboard = [
                            [InlineKeyboardButton("Join Channel 🔗", url=channel_link)],
                            [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                        ]
                    else:
                        # Check if the access code is already used by another user
                        existing_users = self.sub_manager.get_users_by_code(access_code)
                        self.logger.info(f"Existing users with access code {access_code}: {existing_users}")

                        # Check if there are multiple users with this access code
                        # OR if the current user is not in the list of existing users
                        code_used_by_other = len(existing_users) > 1 or (existing_users and str(user_id) not in [str(u) for u in existing_users])
                        self.logger.info(f"Code used by other check: {code_used_by_other}, existing users: {existing_users}")

                        if code_used_by_other:
                            message_text = "⚠️ This access code is already associated with another user and cannot be used again. Please try a different access code or contact support for assistance.\n\nAccess code: " + access_code
                        else:
                            message_text = "Thank you for submitting your information. We'll review your request."

                        keyboard = [
                            [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                        ]
                else:
                    self.logger.error(f"Failed to save verification data for user {user_id}")
                    message_text = "There was an issue submitting your verification request. Our team has been notified and will assist you shortly."

                    keyboard = [
                        [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                    ]

                await query.message.reply_text(
                    message_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

                return WAITING_FOR_CODE
            except Exception as e:
                self.logger.error(f"Error in verification_use_existing: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        elif callback_data == 'verification_update_details':
            try:
                user_id = query.from_user.id
                self.logger.info(f"User {user_id} chose to update their details")

                # Ask for name first
                await query.message.reply_text(
                    "👤Your Good Name?",
                    reply_markup=ForceReply(selective=True)
                )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_NAME
                return WAITING_FOR_NAME
            except Exception as e:
                self.logger.error(f"Error in verification_update_details: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        elif callback_data == 'verification_request_use_existing':
            try:
                user_id = query.from_user.id
                self.logger.info(f"User {user_id} chose to proceed with existing details for verification request")

                # Ask for account number
                await query.message.reply_text(
                    "Please enter your Trading Account number (10 digits):",
                    reply_markup=ForceReply(selective=True)
                )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT
                return WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT
            except Exception as e:
                self.logger.error(f"Error in verification_request_use_existing: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        elif callback_data == 'verification_request_update_details':
            try:
                user_id = query.from_user.id
                self.logger.info(f"User {user_id} chose to update their details for verification request")

                # Clear any previous data
                context.user_data.clear()
                self.logger.info(f"Cleared context.user_data for user {user_id}")

                # Ask for name first
                await query.message.reply_text(
                    "👤Your Good Name?",
                    reply_markup=ForceReply(selective=True)
                )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_VERIFICATION_REQUEST_NAME
                return WAITING_FOR_VERIFICATION_REQUEST_NAME
            except Exception as e:
                self.logger.error(f"Error in verification_request_update_details: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        # Handler for request trading experience selection
        elif callback_data in ["beginner_request", "intermediate_request", "pro_request"]:
            try:
                user_id = query.from_user.id
                # Extract the actual experience level by removing the '_request' suffix
                experience = callback_data.replace("_request", "")
                self.logger.info(f"User {user_id} selected trading experience for request: {experience}")

                # Store the trading experience in context
                context.user_data['request_trading_experience'] = experience

                # Finalize the request submission
                request_text = context.user_data.get('request_text', 'No request text provided')

                # Prepare user data for master_user_data collection with trading experience
                user_details = {
                    'name': context.user_data.get('request_name', 'Unknown'),
                    'email': context.user_data.get('request_email', 'Unknown'),
                    'whatsapp': context.user_data.get('request_whatsapp', 'Unknown'),
                    'trading_experience': experience
                }

                master_user_data = {
                    'user_id': user_id,
                    'name': user_details['name'],
                    'email': user_details['email'],
                    'whatsapp': user_details['whatsapp'],
                    'trading_experience': experience,
                    'subscription': 'inactive',  # Mark as inactive since this is just a request
                    'user_verify': False,  # Default to not verified
                    'broker_reg': False,   # User doesn't have an access code yet
                    'user_status': 'new',  # New user without access code
                    'user_verify_status': 'unknown',
                    'registration_time': datetime.now(timezone.utc),
                    'telegram_bot_id': self.get_bot_id_safely(query)  # Add the Telegram bot ID
                }

                # Save the user data to the master_user_data collection
                success, auto_verified = self.sub_manager.save_to_master_user_data(master_user_data)
                self.logger.info(f"User {user_id} added to master_user_data collection with trading experience: {experience}")

                # Note: We don't need to handle auto_verified here since this is for support requests
                # and the user doesn't have an access code (broker_reg=False)

                # Get the request_id from context
                request_id = context.user_data.get('request_id')

                if request_id:
                    # Update the support request with user details
                    try:
                        # Use the subscription manager to update the support request
                        result = self.sub_manager.db.db.support_request.update_one(
                            {'request_id': request_id},
                            {
                                '$set': {
                                    'user_details': user_details,
                                    'updated_at': datetime.now(timezone.utc)
                                }
                            }
                        )
                        self.logger.info(f"Updated support request {request_id} with user details: {result.modified_count} document(s) modified")
                    except Exception as e:
                        self.logger.error(f"Error updating support request {request_id} with user details: {str(e)}")
                else:
                    # If no request_id exists, create a new support request
                    try:
                        # Get the bot ID
                        telegram_bot_id = self.get_bot_id_safely(query)
                        request_id, success = self.sub_manager.create_support_request(user_id, request_text, user_details, telegram_bot_id=telegram_bot_id)
                        if success:
                            self.logger.info(f"Created new support request {request_id} for user {user_id} with user details and bot ID {telegram_bot_id}")
                            # Store the request_id in context for future reference
                            context.user_data['request_id'] = request_id
                        else:
                            self.logger.error(f"Failed to create support request for user {user_id}")
                    except Exception as e:
                        self.logger.error(f"Error creating support request with user details: {str(e)}")

                # Send confirmation message
                message_text = "Your Request has been submitted successfully ✅ Thank you for sharing your valuable info, our team will get back to you soon 🙏"
                keyboard = [[InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]]

                await query.message.reply_text(
                    message_text,
                    reply_markup=InlineKeyboardMarkup(keyboard)
                )

                return WAITING_FOR_CODE
            except Exception as e:
                self.logger.error(f"Error handling request trading experience: {str(e)}")
                traceback.print_exc()
                await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                return WAITING_FOR_CODE

        # Old verification request handler - keeping for reference
        elif callback_data == 'submit_verification_request':
            # Start the verification request process by asking for the account number
            message_text = "To submit a Manual verification request, we need some information from you.\n\nPlease enter your Trading Account number (10 digits):"

            # Send a message asking for the account number
            await query.message.reply_text(
                message_text,
                reply_markup=ForceReply(selective=True)
            )

            # Clear any previous data
            context.user_data.clear()

            # Import the new state here to avoid circular imports
            from src.handlers.message_handlers import WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT
            return WAITING_FOR_VERIFICATION_REQUEST_ACCOUNT

        elif callback_data == 'notify_account_linked':
            message_text = "We will notify you once your account is linked. Thank you for your patience."
            keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        # 'change_ib_reapply' handler removed - now using 'partner_change_start'

        elif callback_data == 'make_first_deposit':
            message_text = "To make your first deposit, please follow these steps: [Instructions for making a deposit]"
            keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        elif callback_data == 'notify_verification_done':
            message_text = "Roger That!🤖"
            keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        # Handlers for Claim Membership workflow
        elif callback_data == 'claim_membership':
            # Show the Claim Membership submenu
            # Get broker referral link from config
            broker_referral_link = self.get_broker_referral_link()

            message_text = (
                "Your membership with all these benefits awaits you 💰\n\n"
                "Daily Setups and Highly Accurate Signals 🎯\n"
                "Chart Updates 📊\n"
                "Exclusive Forex Trading Course 📹\n"
                "News info & Data releases 📰\n"
                "Priority to your Account-related issues 🥇\n"
                "Premium support on Withdrawals/Deposits & Trades 🤝\n\n"
                f"To join open an account using the link: {broker_referral_link}\n"
                "And proceed with your verification 🚥"
            )

            keyboard = [
                [InlineKeyboardButton("Account Created ✅", callback_data='account_created')],
                [InlineKeyboardButton("Steps to Create an Account 🪪", callback_data='claim_show_steps')],
                [InlineKeyboardButton("Facing issues in verifying/creating an account 🆘", callback_data='submit_request_start')],
                [InlineKeyboardButton("Change Partner to Verify under US 🥷", callback_data='partner_change_start')],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Use smart message updating for smooth transitions
            success = await self.message_helpers.update_message_smartly(
                query.message, message_text, reply_markup,
                self.image_manager, 'claim_membership', context
            )

            if not success:
                self.logger.warning("Smart update failed, falling back to new message")
                await self.send_message_with_claim_membership_image(query.message.chat, message_text, reply_markup, context)

            return WAITING_FOR_CODE

        # Placeholder handlers for other Claim Membership options
        elif callback_data == 'claim_show_steps':
            # Get broker referral link from config
            broker_referral_link = self.get_broker_referral_link()

            message_text = (
                "Follow these steps to create your account:\n\n"
                f"1. Click on the link: {broker_referral_link}\n"
                "2. Fill in your personal details\n"
                "3. Complete the KYC verification\n"
                "4. Create a trading account\n"
                "5. Return here and click 'Account Created ✅' to proceed with verification"
            )
            keyboard = [
                [InlineKeyboardButton("Opened the Account ✅", callback_data="reg_opened_account")],
                [InlineKeyboardButton("Have an account but not under this link 🔁", callback_data="partner_change_start")],
                [InlineKeyboardButton("Back to Claim Membership", callback_data="claim_membership")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        # 'claim_facing_issues' handler removed - now using 'submit_request_start'

        # 'claim_change_partner' handler removed - now using 'partner_change_start'

        # Handlers for Verify Membership workflow
        elif callback_data == 'verify_membership':
            message_text = "To Claim your membership make sure you have an active account with Verified KYC and have placed trades with an initial deposit of up to 200$ ✅"

            keyboard = [
                [InlineKeyboardButton("Verify my Membership 🔍", callback_data='verify_trigger_check')],
                [InlineKeyboardButton("Renew Membership ♻️", callback_data='verify_renew')],
                [InlineKeyboardButton("Have an Account under a different link ⁉️", callback_data='partner_change_start')],
                [InlineKeyboardButton("Need Help? ⚙️", callback_data='submit_request_start')],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Use smart message updating for smooth transitions
            success = await self.message_helpers.update_message_smartly(
                query.message, message_text, reply_markup,
                self.image_manager, 'verify_membership', context
            )

            if not success:
                self.logger.warning("Smart update failed, falling back to new message")
                await self.send_message_with_verify_membership_image(query.message.chat, message_text, reply_markup, context)

            return WAITING_FOR_CODE

        elif callback_data == 'verify_trigger_check':
            # Check if the user is already in the database and under verification
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            # Get user data
            user_data = self.sub_manager.get_user_data(user_id)

            if user_data:
                if is_verified and status == 'active':
                    # User is already verified and has an active subscription
                    user_name = user_data.get('name', 'there')
                    message_text = f"Welcome back {user_name}! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                    # Create a one-time use invite link
                    channel_link = await self.create_one_time_invite_link(context)

                    keyboard = [
                        [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                        [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                        [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                        [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+971585727623")],
                        [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE
                elif status == 'denied':
                    # User's verification was denied
                    user_name = user_data.get('name', 'there')
                    message_text = status_message

                    keyboard = [
                        [InlineKeyboardButton("Reverify 🔄", callback_data="reg_start_verification")],
                        [InlineKeyboardButton("Back to Verify Membership", callback_data="verify_membership")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE
                elif status == 'inactive' and not is_verified:
                    # User is already in the database but still under verification
                    user_name = user_data.get('name', 'there')
                    message_text = f"Welcome back {user_name}! We already have your details.\n\nWe will soon verify your account and notify you once verified.⏳"

                    keyboard = [
                        [InlineKeyboardButton("Back to Verify Membership", callback_data="verify_membership")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE

            # User is not in the database or not under verification
            message_text = "Verify your membership by submitting your Trading account number 🪪"

            # Send a message asking for the account number
            await query.message.reply_text(
                message_text,
                reply_markup=ForceReply(selective=True)
            )

            # Clear any previous data
            context.user_data.clear()

            # Import the new state here to avoid circular imports
            from src.handlers.message_handlers import WAITING_FOR_VERIFY_ACCOUNT_NUMBER
            return WAITING_FOR_VERIFY_ACCOUNT_NUMBER

        # Handler for Renew Membership button
        elif callback_data == 'verify_renew':
            # Check if the user is already in the database and under verification
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            # Get user data
            user_data = self.sub_manager.get_user_data(user_id)

            if user_data:
                if is_verified and status == 'active':
                    # User is already verified and has an active subscription
                    user_name = user_data.get('name', 'there')
                    message_text = f"Welcome back {user_name}! Your profile is verified ✅ You are an exclusive Member With us 🥷"

                    # Create a one-time use invite link
                    channel_link = await self.create_one_time_invite_link(context)

                    keyboard = [
                        [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                        [InlineKeyboardButton("Explore my Benefits 🌍", callback_data="verify_explore_offerings")],
                        [InlineKeyboardButton("My Community Ranking 🏅", callback_data="community_ranking")],
                        [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", url="http://wa.me/+971585727623")],
                        [InlineKeyboardButton("Need More Support?", callback_data="submit_request_start")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE
                elif status == 'denied':
                    # User's verification was denied
                    user_name = user_data.get('name', 'there')
                    message_text = status_message

                    keyboard = [
                        [InlineKeyboardButton("Reverify 🔄", callback_data="reg_start_verification")],
                        [InlineKeyboardButton("Back to Verify Membership", callback_data="verify_membership")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE
                elif status == 'inactive' and not is_verified:
                    # User is already in the database but still under verification
                    user_name = user_data.get('name', 'there')
                    message_text = f"Welcome back {user_name}! We already have your details.\n\nWe will soon verify your account and notify you once verified.⏳"

                    keyboard = [
                        [InlineKeyboardButton("Back to Verify Membership", callback_data="verify_membership")],
                        [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE

            # User is not in the database or not under verification
            message_text = "Verify your membership by submitting your Trading account number 🪪"

            # Send a message asking for the account number
            await query.message.reply_text(
                message_text,
                reply_markup=ForceReply(selective=True)
            )

            # Clear any previous data
            context.user_data.clear()

            # Import the new state here to avoid circular imports
            from src.handlers.message_handlers import WAITING_FOR_VERIFY_ACCOUNT_NUMBER
            return WAITING_FOR_VERIFY_ACCOUNT_NUMBER

        # 'verify_diff_link' handler removed - now using 'partner_change_start'

        # 'verify_help' handler removed - now using 'submit_request_start'

        # Handlers for verification result actions
        elif callback_data == 'verify_claim_existing':
            user_id = query.from_user.id

            # Get the account number from context if available
            account_number = context.user_data.get('verify_account_number', None)

            if account_number:
                # Check if this user is already associated with this account
                existing_users = self.sub_manager.get_users_by_code(account_number)
                user_has_access = str(user_id) in [str(u) for u in existing_users]

                if user_has_access:
                    # User already has access to this account
                    user_data = self.sub_manager.get_user_data(user_id)

                    if user_data:
                        # Check if the user's subscription is expired
                        if user_data.get('user_status') == 'expired' or user_data.get('subscription') == 'expired':
                            message_text = (
                                f"Your subscription with access code {user_data['access_code']} has expired.\n\n"
                                f"Please apply for a new membership or contact support for assistance."
                            )
                            keyboard = [
                                [InlineKeyboardButton("Retry Verification ✅", callback_data='reg_start_verification')],
                                [InlineKeyboardButton("Apply for Membership 🏆", callback_data='verify_apply_new')],
                                [InlineKeyboardButton("Submit a Manual verification Request 🔍", callback_data='verification_request_start')],
                                [InlineKeyboardButton("Change your Partner/IB 🥷", callback_data='partner_change_start')],
                                [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                            ]
                            reply_markup = InlineKeyboardMarkup(keyboard)

                            # Edit the message safely
                            success = await self.edit_message_safely(query.message, message_text, reply_markup)

                            if not success:
                                await query.message.reply_text(message_text, reply_markup=reply_markup)

                            return WAITING_FOR_CODE
                        else:
                            message_text = (
                                f"Welcome back! You already have active access with code: {user_data['access_code']}\n\n"
                                f"Your subscription is active since: {user_data.get('subscription_time', user_data.get('registration_time')).strftime('%Y-%m-%d %H:%M:%S') if user_data.get('subscription_time') or user_data.get('registration_time') else 'Unknown'}\n\n"
                                f"You can now access all the benefits of your membership."
                            )
                    else:
                        message_text = "Welcome back! You can now access all the benefits of your membership."
                else:
                    # User doesn't have access to this account yet, but the account exists
                    message_text = (
                        "This account exists in our system but is not yet associated with your user ID.\n\n"
                        "Would you like to claim this account?"
                    )
                    keyboard = [
                        [InlineKeyboardButton("Claim this Account 🔑", callback_data='reg_start_verification')],
                        [InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]
                    ]
                    reply_markup = InlineKeyboardMarkup(keyboard)

                    # Edit the message safely
                    success = await self.edit_message_safely(query.message, message_text, reply_markup)

                    if not success:
                        await query.message.reply_text(message_text, reply_markup=reply_markup)

                    return WAITING_FOR_CODE
            else:
                # No account number in context, show generic message
                message_text = "Welcome back! You can now access all the benefits of your membership."

            keyboard = [[InlineKeyboardButton("Back to Menu", callback_data="back_to_menu")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        elif callback_data == 'verify_explore_offerings':
            message_text = (
                "Here are some of our exclusive offerings for members:\n\n"
                "Daily Setups and Highly Accurate Signals 🎯\n"
                "Chart Updates 📊\n"
                "Exclusive Forex Trading Course 📹\n"
                "News info & Data releases 📰\n"
                "Priority to your Account-related issues 🥇\n"
                "Premium support on Withdrawals/Deposits & Trades 🤝\n\n"
                "You have been added on our space with LEVEL 1"
            )

            # Create a one-time use invite link
            channel_link = await self.create_one_time_invite_link(context)

            keyboard = [
                [InlineKeyboardButton("Access Premium space 🏆", url=channel_link)],
                [InlineKeyboardButton("Back to Previous Menu", callback_data="back_to_verification")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Use smart message updating for smooth transitions
            success = await self.message_helpers.update_message_smartly(
                query.message, message_text, reply_markup,
                self.image_manager, 'membership_offer', context
            )

            if not success:
                self.logger.warning("Smart update failed, falling back to new message")
                await self.send_message_with_membership_offer_image(query.message.chat, message_text, reply_markup, context)

        elif callback_data == 'community_ranking':
            message_text = (
                "Since you're a new trader with us we are collecting more info and building your rank, "
                "check this after few weeks and more about benefits on having a higher rank in the community."
            )

            keyboard = [
                [InlineKeyboardButton("Back to Previous Menu", callback_data="back_to_verification")],
                [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                await query.message.reply_text(message_text, reply_markup=reply_markup)

        elif callback_data == 'verify_apply_new':
            # Redirect to the Claim Membership flow
            return await self.handle_callback(update, context, callback_data='claim_membership')

        elif callback_data == 'verify_submit_request':
            # Reuse the new verification_request_start handler
            return await self.handle_callback(update, context, callback_data='verification_request_start')

        # 'verify_change_ib' handler removed - now using 'partner_change_start'

        # Handlers for Withdrawals & Deposits workflow
        elif callback_data == 'wd_start':
            message_text = (
                "🔴 Our exclusive withdrawal and deposit department is available for verified users only, "
                "make sure to contact the department with your registered WhatsApp number, "
                "for additional verifications you can reach out to the team with your :\n\n"
                "Registered Email with us 📧\n"
                "Your Requirement for Withdrawal or Deposit 💸\n"
                "Requested Amount 💵\n"
                "Payment Method 💳"
            )

            keyboard = [
                [InlineKeyboardButton("Contact Withdrawals/Deposit Team 🔰", callback_data='wd_contact_team')],
                [InlineKeyboardButton("Submit a request 🪃", callback_data='submit_request_start')],
                [InlineKeyboardButton("Back to Main Menu", callback_data='back_to_menu')]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Use smart message updating for smooth transitions
            success = await self.message_helpers.update_message_smartly(
                query.message, message_text, reply_markup,
                self.image_manager, 'withdrawal', context
            )

            if not success:
                self.logger.warning("Smart update failed, falling back to new message")
                await self.send_message_with_withdrawal_image(query.message.chat, message_text, reply_markup, context)

            return WAITING_FOR_CODE

        elif callback_data == 'wd_contact_team':
            # Check the user's subscription status
            user_id = query.from_user.id
            is_verified, status, status_message = self.sub_manager.check_subscription_status_and_get_message(user_id)
            self.logger.info(f"Subscription check result for user {user_id}: is_verified={is_verified}, status={status}, status_message={status_message}")

            if status == 'active':
                # User is verified - show contact information
                message_text = "Verified members can contact the <NAME_EMAIL> or via WhatsApp at +1234567890."
                keyboard = [
                    [InlineKeyboardButton("Back to Withdrawals & Deposits", callback_data="wd_start")],
                    [InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    await query.message.reply_text(message_text, reply_markup=reply_markup)
            else:
                # User is not verified - show status message
                await query.answer(status_message, show_alert=True)

        # 'wd_submit_request' handler removed - now using 'submit_request_start'



        # Handlers for Submit a Request workflow
        elif callback_data == 'submit_request_start':
            try:
                user_id = query.from_user.id
                self.logger.info(f"Starting submit_request_start for user {user_id}")

                # Clear any previous data
                context.user_data.clear()
                self.logger.info(f"Cleared context.user_data for user {user_id}")

                message_text = "Our team is here to assist you in your entire journey, kindly let us know how we can help you 🤝"

                # Use smart message updating for smooth transitions
                success = await self.message_helpers.update_message_smartly(
                    query.message, message_text, None,
                    self.image_manager, 'support', context
                )

                if success:
                    # Send a follow-up message with ForceReply to get the user's request
                    await query.message.reply_text(
                        "Please describe your request:",
                        reply_markup=ForceReply(selective=True)
                    )
                else:
                    self.logger.warning("Smart update failed, falling back to new message")
                    # Send a new message with ForceReply
                    sent_message = await self.send_message_with_support_image(query.message.chat, message_text, None, context)
                    await sent_message.reply_text(
                        "Please describe your request:",
                        reply_markup=ForceReply(selective=True)
                    )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_REQUEST_TEXT
                self.logger.info(f"Returning state WAITING_FOR_REQUEST_TEXT for user {user_id}")
                return WAITING_FOR_REQUEST_TEXT
            except Exception as e:
                self.logger.error(f"Error in submit_request_start: {str(e)}")
                traceback.print_exc()
                # Try to send an error message to the user
                try:
                    await query.message.reply_text("Sorry, there was an error processing your request. Please try again later.")
                except Exception:
                    pass
                return WAITING_FOR_CODE

        # Handlers for the support request workflow
        elif callback_data == 'support_add_details':
            message_text = "Please provide any additional details:"

            # Send a message asking for more details
            await query.message.reply_text(
                message_text,
                reply_markup=ForceReply(selective=True)
            )

            # Import the new state here to avoid circular imports
            from src.handlers.message_handlers import WAITING_FOR_REQUEST_TEXT
            return WAITING_FOR_REQUEST_TEXT

        elif callback_data == 'support_finalize_request':
            user_id = query.from_user.id

            # Check if the user exists in the master_user_data collection
            user_exists = self.sub_manager.check_user_exists_in_master(user_id)

            if not user_exists:
                # User is not in master_user_data collection, collect additional information
                message_text = "Kindly help us with more info so our team can get back to you."
                await query.message.reply_text(message_text)

                # Ask for name
                await query.message.reply_text(
                    "👤Your Good Name?",
                    reply_markup=ForceReply(selective=True)
                )

                # Import the new state here to avoid circular imports
                from src.handlers.message_handlers import WAITING_FOR_REQUEST_NAME
                return WAITING_FOR_REQUEST_NAME
            else:
                # User already exists in master_user_data, finalize the request
                request_id = context.user_data.get('request_id')

                # Get user data from the master_user_data collection
                user_data = self.sub_manager.get_master_user_data(user_id)

                if request_id:
                    # Update the support request with user details from master_user_data
                    if user_data:
                        user_details = {
                            'name': user_data.get('name', 'Unknown'),
                            'email': user_data.get('email', 'Unknown'),
                            'whatsapp': user_data.get('whatsapp', 'Unknown'),
                            'trading_experience': user_data.get('trading_experience', 'Unknown')
                        }

                        # Update the support request with user details and mark as submitted
                        try:
                            # Use the subscription manager to update the support request
                            result = self.sub_manager.db.db.support_request.update_one(
                                {'request_id': request_id},
                                {
                                    '$set': {
                                        'user_details': user_details,
                                        'status': 'submitted',
                                        'updated_at': datetime.now(timezone.utc)
                                    }
                                }
                            )
                            self.logger.info(f"Updated support request {request_id} with user details and marked as submitted: {result.modified_count} document(s) modified")
                        except Exception as e:
                            self.logger.error(f"Error updating support request {request_id}: {str(e)}")
                        self.logger.info(f"Updated support request {request_id} with user details and marked as submitted")
                    else:
                        # Just mark the request as submitted
                        try:
                            # Use the subscription manager to update the support request
                            result = self.sub_manager.db.db.support_request.update_one(
                                {'request_id': request_id},
                                {
                                    '$set': {
                                        'status': 'submitted',
                                        'updated_at': datetime.now(timezone.utc)
                                    }
                                }
                            )
                            self.logger.info(f"Marked support request {request_id} as submitted: {result.modified_count} document(s) modified")
                        except Exception as e:
                            self.logger.error(f"Error marking support request {request_id} as submitted: {str(e)}")
                        self.logger.info(f"Marked support request {request_id} as submitted")
                else:
                    # If no request_id exists, create a new support request
                    request_text = context.user_data.get('request_text', 'No request text provided')

                    # Prepare user details if available
                    user_details = None
                    if user_data:
                        user_details = {
                            'name': user_data.get('name', 'Unknown'),
                            'email': user_data.get('email', 'Unknown'),
                            'whatsapp': user_data.get('whatsapp', 'Unknown')
                        }

                    # Create a new support request with status 'submitted'
                    # Get the bot ID
                    telegram_bot_id = self.get_bot_id_safely(query)
                    request_id, success = self.sub_manager.create_support_request(user_id, request_text, user_details, telegram_bot_id=telegram_bot_id)
                    if success:
                        # Mark the request as submitted
                        try:
                            # Use the subscription manager to update the support request
                            result = self.sub_manager.db.db.support_request.update_one(
                                {'request_id': request_id},
                                {
                                    '$set': {
                                        'status': 'submitted',
                                        'updated_at': datetime.now(timezone.utc)
                                    }
                                }
                            )
                            self.logger.info(f"Created and marked support request {request_id} as submitted: {result.modified_count} document(s) modified")
                        except Exception as e:
                            self.logger.error(f"Error marking new support request {request_id} as submitted: {str(e)}")
                        self.logger.info(f"Created and submitted support request {request_id} for user {user_id} with bot ID {telegram_bot_id}")
                    else:
                        self.logger.error(f"Failed to create support request for user {user_id}")

                # Send confirmation message
                message_text = "Your Request has been submitted successfully ✅ Thank you for sharing your valubale info, our team will get back to you soon 🙏"
                keyboard = [[InlineKeyboardButton("Back to Main Menu", callback_data="back_to_menu")]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                # Edit the message safely
                success = await self.edit_message_safely(query.message, message_text, reply_markup)

                if not success:
                    await query.message.reply_text(message_text, reply_markup=reply_markup)

                return WAITING_FOR_CODE

    async def handle_experience(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle trading experience selection and complete registration"""
        query = update.callback_query
        await query.answer()

        user_id = query.from_user.id
        experience = query.data

        # Collect user details from context
        user_details = {
            'name': context.user_data.get('name', ''),
            'email': context.user_data.get('email', ''),
            'whatsapp': context.user_data.get('whatsapp', ''),
            'trading_experience': experience,
        }

        # Create clean subscription data
        subscription_data = {
            'user_id': user_id,
            'access_code': context.user_data.get('access_code'),
            'subscription_time': datetime.now(timezone.utc),
            'user_details': user_details,
            'telegram_bot_id': self.get_bot_id_safely(query)
        }

        success, message = self.sub_manager.add_subscription(
            user_id,
            context.user_data.get('access_code'),
            subscription_data
        )

        if success:
            # Create a one-time use invite link
            invite_link = await self.create_one_time_invite_link(context)

            keyboard = [[InlineKeyboardButton("Join Channel", url=invite_link)]]
            reply_markup = InlineKeyboardMarkup(keyboard)
            message_text = f"✅ Registration complete!\nThank you for providing your information!\nClick below to join:"

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                # Only if editing fails completely, send a new message
                self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                await query.message.reply_text(
                    message_text,
                    reply_markup=reply_markup
                )

            # Save user information to telegram_bots collection
            try:
                # Get the bot's ID from context or config
                bot_id = context.bot.id  # This gets the current bot's Telegram ID

                # Prepare data to update for this specific user interaction
                user_interaction_data = {
                    "channel_link_sent": True,
                    "interaction_time": datetime.now(timezone.utc),
                }

                # Call method to update the specific bot's data
                success, message = self.sub_manager.update_bot_user_interaction(
                    bot_id,
                    user_id,
                    user_interaction_data
                )

                if success:
                    self.logger.info(f"User {user_id} interaction saved for bot {bot_id}")
                else:
                    self.logger.error(f"Error saving user {user_id} interaction for bot {bot_id}: {message}")
            except Exception as e:
                self.logger.error(f"Error in bot data update: {str(e)}")
        else:
            message_text = f"❌ {message}"
            keyboard = [[InlineKeyboardButton("Start Again", callback_data='back_to_menu')]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Edit the message safely
            success = await self.edit_message_safely(query.message, message_text, reply_markup)

            if not success:
                # Only if editing fails completely, send a new message
                self.logger.warning(f"Failed to edit message for user {user_id}, sending a new message")
                await query.message.reply_text(
                    message_text,
                    reply_markup=reply_markup
                )

            # Send an additional message to try again
            await query.message.reply_text(
                "Try again or start over:",
                reply_markup=reply_markup
            )

        return ConversationHandler.END

    async def create_one_time_invite_link(self, context):
        """Create a one-time use invite link for the channel"""
        try:
            # Get chat_id from config
            chat_id_str = self.config.get('channel_id')
            if not chat_id_str:
                self.logger.error("Channel ID not found in configuration.")
                # Return a fallback link
                return self.config.get('channel_link', 'https://t.me/your_channel')

            # Format the chat_id properly for Telegram API
            # For channels and supergroups, the ID must start with -100
            # If it already has -100, keep it, otherwise add it
            if not chat_id_str.startswith('-100'):
                chat_id_str = f"-100{chat_id_str}"

            # Convert to integer
            chat_id = int(chat_id_str)
            self.logger.info(f"Attempting to create invite link for chat_id: {chat_id}")

            invite_link_obj = await context.bot.create_chat_invite_link(
                chat_id=chat_id,
                member_limit=1,  # CRUCIAL: Limits the link to exactly one use
                expire_date=None  # No expiration date, but it will expire after one use
            )
            self.logger.info(f"Created one-time invite link: {invite_link_obj.invite_link}")
            return invite_link_obj.invite_link

        except (TelegramError, ValueError, TypeError) as e:
            # Catch specific errors: Telegram issues, ValueError/TypeError if chat_id is invalid format
            self.logger.error(f"Error creating invite link for chat {chat_id_str if 'chat_id_str' in locals() else 'unknown'}: {e}")
            # Try with a different format as fallback
            try:
                if 'chat_id_str' in locals() and chat_id_str.startswith('-100'):
                    # Try without the -100 prefix
                    chat_id = int(chat_id_str.replace('-100', ''))
                    self.logger.info(f"Retrying with alternative chat_id format: {chat_id}")
                    invite_link_obj = await context.bot.create_chat_invite_link(
                        chat_id=chat_id,
                        member_limit=1,
                        expire_date=None
                    )
                    self.logger.info(f"Created one-time invite link with alternative format: {invite_link_obj.invite_link}")
                    return invite_link_obj.invite_link
            except Exception as retry_error:
                self.logger.error(f"Retry also failed: {retry_error}")

            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')
        except Exception as e:
            self.logger.error(f"Unexpected error creating invite link: {e}")
            # Return a fallback link
            return self.config.get('channel_link', 'https://t.me/your_channel')
